'use client';

import { useState, useEffect } from 'react';
import { Bo<PERSON>, Send, BarChart3, MessageSquare, Lightbulb, TrendingUp } from 'lucide-react';
import QueryInput from './QueryInput';
import ResultsDisplay from './ResultsDisplay';
import ChatInterface from './ChatInterface';

interface QueryResult {
  id: string;
  query: string;
  sql: string;
  data: any[];
  analysis: string;
  timestamp: Date;
  visualization?: string;
  success?: boolean;
  cached?: boolean;
}

interface QueryContext {
  query: string;
  results: any[];
  sql: string;
  timestamp: number;
}

interface UnifiedInterfaceProps {
  connectionData: any;
  onQuerySubmit: (query: string) => void;
  isProcessing: boolean;
  queryHistory: QueryResult[];
  queryContext: QueryContext | null;
}

export default function UnifiedInterface({
  connectionData,
  onQuerySubmit,
  isProcessing,
  queryHistory,
  queryContext
}: UnifiedInterfaceProps) {
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [currentView, setCurrentView] = useState<'input' | 'results' | 'chat'>('input');

  // تحديث العرض تلقائياً بناءً على الحالة
  useEffect(() => {
    if (queryHistory.length > 0 && !isProcessing) {
      setCurrentView('results');
      setShowSuggestions(false);
    }
  }, [queryHistory, isProcessing]);

  // إخفاء الاقتراحات عند بدء الكتابة
  const handleQueryStart = () => {
    setShowSuggestions(false);
  };

  const handleQuerySubmit = (query: string) => {
    setCurrentView('input');
    onQuerySubmit(query);
  };

  const quickSuggestions = [
    {
      icon: TrendingUp,
      title: 'أكثر المنتجات مبيعاً',
      query: 'أعرض لي أكثر 5 منتجات مبيعاً',
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: BarChart3,
      title: 'مقارنة المبيعات',
      query: 'قارن بين مبيعات هذا الشهر والشهر الماضي',
      color: 'from-green-500 to-green-600'
    },
    {
      icon: Bot,
      title: 'أفضل الفروع',
      query: 'ما هي أفضل الفروع أداءً؟',
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: MessageSquare,
      title: 'أكثر العملاء شراءً',
      query: 'أكثر العملاء شراءً في الربع الأخير',
      color: 'from-orange-500 to-orange-600'
    }
  ];

  return (
    <div className="space-y-6">
      {/* إحصائيات سريعة */}
      {queryHistory.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-lg shadow border border-gray-200/50 dark:border-gray-700/50 p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                <BarChart3 className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الاستعلامات</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">{queryHistory.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-lg shadow border border-gray-200/50 dark:border-gray-700/50 p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
                <TrendingUp className="w-4 h-4 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">آخر نتيجة</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {queryHistory[0]?.data?.length || 0} صف
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-lg shadow border border-gray-200/50 dark:border-gray-700/50 p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-3">
                <MessageSquare className="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">الدردشة</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {queryContext ? 'نشطة' : 'غير نشطة'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* منطقة الاستعلام الرئيسية */}
      <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50">
        <div className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center mr-3">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                الواجهة الذكية للاستعلامات
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                اسأل أي سؤال عن بياناتك بالعربية وسأقوم بتحليلها لك
              </p>
            </div>
          </div>

          <QueryInput
            onSubmit={handleQuerySubmit}
            isProcessing={isProcessing}
            placeholder="مثال: أعرض لي أكثر 5 منتجات مبيعاً هذا الشهر"
          />

          {/* مؤشر الحالة */}
          {isProcessing && (
            <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
                <span className="text-sm text-blue-700 dark:text-blue-300">
                  جاري تحليل الاستعلام وجلب البيانات...
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* الاقتراحات السريعة */}
      {showSuggestions && queryHistory.length === 0 && (
        <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-6">
          <div className="flex items-center mb-4">
            <Lightbulb className="w-5 h-5 text-yellow-500 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              اقتراحات سريعة
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {quickSuggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleQuerySubmit(suggestion.query)}
                disabled={isProcessing}
                className="group p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg hover:shadow-md transition-all duration-200 text-right border border-gray-200/50 dark:border-gray-600 disabled:opacity-50"
              >
                <div className="flex items-center mb-2">
                  <div className={`w-8 h-8 bg-gradient-to-r ${suggestion.color} rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform`}>
                    <suggestion.icon className="w-4 h-4 text-white" />
                  </div>
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {suggestion.title}
                  </h4>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300">
                  {suggestion.query}
                </p>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* منطقة النتائج والدردشة */}
      {queryHistory.length > 0 && (
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* النتائج */}
          <div className="xl:col-span-2">
            <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50">
              <div className="p-6 border-b border-gray-200/50 dark:border-gray-700/50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <BarChart3 className="w-5 h-5 text-blue-600 mr-2" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      النتائج
                    </h3>
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {queryHistory[0].timestamp.toLocaleString('ar-SA')}
                  </div>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {queryHistory[0].query}
                </p>
              </div>
              <div className="p-6">
                <ResultsDisplay
                  queryHistory={[queryHistory[0]]}
                  onQuerySelect={(result) => handleQuerySubmit(result.query)}
                />
              </div>
            </div>
          </div>

          {/* الدردشة التفاعلية */}
          <div className="xl:col-span-1">
            {queryContext ? (
              <ChatInterface
                queryContext={queryContext}
                onNewQuery={handleQuerySubmit}
                connectionData={connectionData}
                fullScreen={true}
              />
            ) : (
              <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-8 text-center h-[500px] flex flex-col justify-center">
                <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  الدردشة التفاعلية
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  ستظهر هنا الدردشة التفاعلية حول النتائج
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* رسالة ترحيبية عند عدم وجود نتائج */}
      {queryHistory.length === 0 && !isProcessing && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-8 text-center border border-blue-200/50 dark:border-gray-600">
          <Bot className="w-16 h-16 text-blue-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            مرحباً بك في وكيل الذكاء الاصطناعي
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            اسأل أي سؤال عن بياناتك وسأقوم بتحليلها وعرض النتائج بطريقة تفاعلية
          </p>
          <div className="flex items-center justify-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
            <span>✨ تحليل ذكي</span>
            <span>📊 رسوم بيانية</span>
            <span>💬 دردشة تفاعلية</span>
          </div>
        </div>
      )}
    </div>
  );
}
