'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Loader2, Lightbulb, TrendingUp, BarChart3 } from 'lucide-react';

interface QueryContext {
  query: string;
  results: any[];
  sql: string;
  timestamp: number;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
  isAnalysis?: boolean;
}

interface ChatInterfaceProps {
  queryContext: QueryContext;
  onNewQuery?: (query: string) => void;
  connectionData?: any;
  fullScreen?: boolean;
}

export default function ChatInterface({ 
  queryContext, 
  onNewQuery, 
  connectionData,
  fullScreen = false 
}: ChatInterfaceProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // رسالة ترحيب عند تحميل سياق جديد
  useEffect(() => {
    if (queryContext && messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'assistant',
        content: `مرحباً! لقد تم تنفيذ الاستعلام: "${queryContext.query}" وحصلنا على ${queryContext.results.length} نتيجة. 

يمكنك الآن أن تسأل أي سؤال حول هذه البيانات مثل:
• لماذا المنتج الأول هو الأكثر مبيعاً؟
• قارن بين النتيجة الأولى والثانية
• ما هي الاتجاهات في هذه البيانات؟
• اعطني توصيات بناءً على هذه النتائج`,
        timestamp: Date.now()
      };
      setMessages([welcomeMessage]);
    }
  }, [queryContext]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: Date.now()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // استدعاء API للتحليل
      const response = await fetch('/api/chat/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: inputValue,
          queryContext
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          content: result.response,
          timestamp: Date.now(),
          isAnalysis: result.type === 'instant_analysis'
        };

        setMessages(prev => [...prev, assistantMessage]);
      } else {
        // إذا كان السؤال يحتاج استعلام جديد
        if (onNewQuery && inputValue.includes('جديد')) {
          const enhancedQuery = `${inputValue} (بناءً على النتائج السابقة: ${queryContext.query})`;
          onNewQuery(enhancedQuery);

          const assistantMessage: ChatMessage = {
            id: (Date.now() + 1).toString(),
            type: 'assistant',
            content: 'جاري تنفيذ استعلام جديد للإجابة على سؤالك...',
            timestamp: Date.now()
          };

          setMessages(prev => [...prev, assistantMessage]);
        } else {
          throw new Error(result.error || 'فشل في التحليل');
        }
      }
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'عذراً، حدث خطأ أثناء معالجة سؤالك. يرجى المحاولة مرة أخرى.',
        timestamp: Date.now()
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };



  const quickActions = [
    {
      icon: TrendingUp,
      label: 'تحليل الاتجاهات',
      action: 'حلل الاتجاهات في هذه البيانات'
    },
    {
      icon: BarChart3,
      label: 'مقارنة العناصر',
      action: 'قارن بين أول ثلاثة عناصر'
    },
    {
      icon: Lightbulb,
      label: 'توصيات',
      action: 'اعطني توصيات بناءً على هذه النتائج'
    }
  ];

  return (
    <div className={`bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 ${
      fullScreen ? 'h-[600px]' : 'h-[500px]'
    } flex flex-col`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200/50 dark:border-gray-700/50">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
            <Bot className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white">
              الدردشة التفاعلية
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              اسأل عن البيانات المعروضة
            </p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 ${
                message.type === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
              }`}
            >
              <div className="flex items-start">
                {message.type === 'assistant' && (
                  <Bot className="w-4 h-4 mr-2 mt-0.5 text-blue-600 dark:text-blue-400" />
                )}
                {message.type === 'user' && (
                  <User className="w-4 h-4 mr-2 mt-0.5" />
                )}
                <div className="whitespace-pre-wrap text-sm">{message.content}</div>
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
              <div className="flex items-center">
                <Loader2 className="w-4 h-4 mr-2 animate-spin text-blue-600" />
                <span className="text-sm text-gray-600 dark:text-gray-400">جاري التفكير...</span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Actions */}
      {!fullScreen && (
        <div className="p-3 border-t border-gray-200/50 dark:border-gray-700/50">
          <div className="flex gap-2 mb-3">
            {quickActions.map((action, index) => (
              <button
                key={index}
                onClick={() => setInputValue(action.action)}
                className="flex items-center gap-1 px-3 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg text-xs hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
              >
                <action.icon className="w-3 h-3" />
                {action.label}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="p-4 border-t border-gray-200/50 dark:border-gray-700/50">
        <div className="flex gap-2">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="اسأل عن البيانات المعروضة..."
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors flex items-center"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
