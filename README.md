# وكيل الذكاء الاصطناعي لـ SQL

نظام ذكي متطور يتيح للمستخدمين التفاعل مع قواعد بيانات SQL Server باللغة العربية الطبيعية، مع إمكانيات تحليل البيانات والتنبؤ باستخدام الذكاء الاصطناعي.

## ✨ الميزات الجديدة (واجهة الدردشة الموحدة)

### 💬 واجهة دردشة موحدة ثورية
- **دردشة واحدة لكل شيء**: استعلامات جديدة وأسئلة تفاعلية في محادثة واحدة
- **مربع إدخال في الأسفل**: تصميم مألوف مثل WhatsApp و ChatGPT
- **ذكاء تلقائي**: يميز بين الاستعلامات الجديدة والأسئلة عن النتائج الموجودة
- **عرض النتائج المدمج**: جداول ورسوم بيانية داخل المحادثة مباشرة

### 🎯 اقتراحات ذكية تفاعلية
- **اقتراحات مرئية** مع أيقونات وأوصاف واضحة
- **نقرة واحدة للتنفيذ**: اختر اقتراحاً وسيتم تنفيذه فوراً
- **تحديث ديناميكي**: الاقتراحات تتغير حسب السياق

### 📊 عرض النتائج المتطور
- **عرض مدمج**: النتائج تظهر كرسائل في المحادثة
- **تبديل سريع**: جداول، رسوم بيانية، وكود SQL في مكان واحد
- **تفاعل مستمر**: اسأل عن النتائج أو اطلب تحليلات إضافية

### 🔄 تجربة مستخدم محسنة
- **مؤشرات حالة واضحة**: تعرف متى يتم تحليل الاستعلام
- **نصائح تفاعلية**: إرشادات تظهر حسب الحاجة
- **حفظ تلقائي**: جميع النتائج محفوظة في سجل منفصل

## المميزات الرئيسية

### 🤖 الذكاء الاصطناعي المتقدم
- **فهم اللغة الطبيعية**: يفهم الاستعلامات باللغة العربية ويحولها إلى SQL
- **تحليل النيات والكيانات**: استخراج ذكي للمعلومات من النصوص
- **توليد SQL تلقائي**: إنشاء استعلامات SQL دقيقة وفعالة

### 📊 التحليل والتصور
- **رسوم بيانية تفاعلية**: عرض البيانات في أشكال مختلفة (أعمدة، خطوط، دوائر)
- **تحليل الأنماط**: اكتشاف الاتجاهات والأنماط في البيانات
- **رؤى ذكية**: توليد تحليلات وتوصيات قابلة للتنفيذ

### 🔗 الاتصال بقواعد البيانات
- **دعم SQL Server**: اتصال آمن ومستقر
- **فهرسة ذكية**: تحليل وفهرسة الجداول والأعمدة تلقائياً
- **حفظ التكوين**: تجنب إعادة الفهرسة للقواعد المعروفة

### 🌐 واجهة مستخدم متطورة
- **دعم RTL**: تصميم مُحسّن للغة العربية
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **وضع داكن**: دعم للوضع الليلي

## التقنيات المستخدمة

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4, Lucide Icons
- **Database**: SQL Server (mssql)
- **AI**: OpenRouter API (Qwen 2.5 72B)
- **Charts**: Recharts
- **Language**: دعم كامل للعربية مع RTL

## التثبيت والتشغيل

### المتطلبات
- Node.js 18+
- SQL Server
- مفتاح API من OpenRouter

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd sql-ai-agent
```

2. **تثبيت المكتبات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.local.example .env.local
```

أضف مفتاح OpenRouter API:
```env
OPENROUTER_API_KEY=your_api_key_here
```

4. **تشغيل التطبيق**
```bash
npm run dev
```

5. **فتح التطبيق**
افتح [http://localhost:3000](http://localhost:3000) في المتصفح

## كيفية الاستخدام

### 1. الاتصال بقاعدة البيانات
- أدخل بيانات الاتصال بـ SQL Server
- اختبر الاتصال
- ابدأ عملية الفهرسة

### 2. طرح الأسئلة
استخدم اللغة العربية الطبيعية مثل:
- "أعرض لي أكثر 10 منتجات مبيعاً"
- "قارن بين مبيعات هذا الشهر والشهر الماضي"
- "ما هي أفضل الفروع أداءً؟"

### 3. تحليل النتائج
- عرض البيانات في جداول ورسوم بيانية
- قراءة التحليلات والرؤى الذكية
- تصدير النتائج

## هيكل المشروع

```
sql-ai-agent/
├── src/
│   ├── app/                 # صفحات Next.js
│   ├── components/          # مكونات React
│   ├── lib/                 # خدمات ومكتبات
│   └── data/               # ملفات البيانات والتكوين
├── public/                 # الملفات العامة
└── docs/                   # الوثائق
```

## المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم:
- فتح Issue في GitHub
- مراجعة الوثائق
- التواصل مع الفريق

## خارطة الطريق

- [ ] دعم قواعد بيانات إضافية (MySQL, PostgreSQL)
- [ ] تحليلات متقدمة والتعلم الآلي
- [ ] تصدير التقارير
- [ ] API للتكامل مع أنظمة أخرى
- [ ] تطبيق موبايل

---

تم تطوير هذا المشروع بـ ❤️ لخدمة المجتمع العربي في مجال تحليل البيانات
