'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Loader2, BarChart3, Table, Code, TrendingUp } from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'result';
  content: string;
  timestamp: number;
  data?: any[];
  sql?: string;
  analysis?: string;
  visualization?: string;
  query?: string;
}

interface UnifiedChatInterfaceProps {
  connectionData: any;
  onQueryResult?: (result: any) => void;
}

export default function UnifiedChatInterface({ connectionData, onQueryResult }: UnifiedChatInterfaceProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [showWelcome, setShowWelcome] = useState(true);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (messages.length > 0) {
      setShowWelcome(false);
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: Date.now()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue;
    setInputValue('');
    setIsLoading(true);

    try {
      // تحديد نوع الرسالة (استعلام جديد أم سؤال عن النتائج)
      const queryKeywords = ['أعرض', 'قارن', 'ما هي', 'أكثر', 'أفضل', 'تفاصيل', 'اعرض', 'كم', 'احسب', 'مجموع', 'متوسط'];
      const isNewQuery = messages.length === 0 ||
        queryKeywords.some(keyword => currentInput.includes(keyword)) ||
        currentInput.includes('؟') && currentInput.length > 20;

      if (isNewQuery) {
        // استعلام جديد
        const response = await fetch('/api/query/process', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: currentInput,
            connectionData
          }),
        });

        const result = await response.json();

        if (response.ok) {
          const resultMessage: ChatMessage = {
            id: (Date.now() + 1).toString(),
            type: 'result',
            content: result.analysis || 'تم تنفيذ الاستعلام بنجاح',
            timestamp: Date.now(),
            data: result.data,
            sql: result.sql,
            analysis: result.analysis,
            visualization: result.visualization,
            query: currentInput
          };

          setMessages(prev => [...prev, resultMessage]);

          // إرسال النتيجة إلى المكون الأب لحفظها في سجل النتائج
          if (onQueryResult) {
            onQueryResult({
              id: resultMessage.id,
              query: currentInput,
              sql: result.sql,
              data: result.data,
              analysis: result.analysis,
              timestamp: new Date(),
              visualization: result.visualization,
              success: true
            });
          }
        } else {
          const errorMessage: ChatMessage = {
            id: (Date.now() + 1).toString(),
            type: 'assistant',
            content: `عذراً، حدث خطأ: ${result.error}`,
            timestamp: Date.now()
          };
          setMessages(prev => [...prev, errorMessage]);
        }
      } else {
        // سؤال عن النتائج الموجودة
        const lastResult = messages.filter(m => m.type === 'result').pop();
        
        const response = await fetch('/api/chat/analyze', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            question: currentInput,
            queryContext: lastResult ? {
              query: lastResult.query,
              results: lastResult.data,
              sql: lastResult.sql,
              timestamp: lastResult.timestamp
            } : null
          }),
        });

        const result = await response.json();

        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          content: result.response || 'لم أتمكن من فهم السؤال',
          timestamp: Date.now()
        };

        setMessages(prev => [...prev, assistantMessage]);
      }
    } catch (error) {
      console.error('خطأ في الشبكة:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.',
        timestamp: Date.now()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // مكون منفصل لعرض النتائج لتجنب مشكلة hooks
  const ResultMessage = ({ message }: { message: ChatMessage }) => {
    const [viewMode, setViewMode] = useState<'table' | 'chart' | 'sql'>('table');

    const renderTable = (data: any[]) => {
      if (!data || data.length === 0) return <p>لا توجد بيانات</p>;

      const columns = Object.keys(data[0]);
      
      return (
        <div className="overflow-x-auto w-full">
          <table className="w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                {columns.map((column) => (
                  <th key={column} className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {column}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {data.slice(0, 10).map((row, index) => (
                <tr key={index}>
                  {columns.map((column) => (
                    <td key={column} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {row[column]}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
          {data.length > 10 && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2 text-center">
              عرض 10 من أصل {data.length} صف
            </p>
          )}
        </div>
      );
    };

    const renderChart = (data: any[]) => {
      if (!data || data.length === 0) return <p>لا توجد بيانات للرسم البياني</p>;

      const columns = Object.keys(data[0]);
      const numericColumns = columns.filter(col => 
        data.some(row => typeof row[col] === 'number')
      );

      if (numericColumns.length === 0) return <p>لا توجد بيانات رقمية للرسم البياني</p>;

      return (
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data.slice(0, 10)}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={columns[0]} />
              <YAxis />
              <Tooltip />
              <Bar dataKey={numericColumns[0]} fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      );
    };

    return (
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
        <div className="flex items-center mb-3">
          <BarChart3 className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <span className="font-medium text-blue-800 dark:text-blue-200">نتائج الاستعلام</span>
        </div>

        <p className="text-sm text-blue-700 dark:text-blue-300 mb-4">{message.content}</p>

        {message.data && message.data.length > 0 && (
          <>
            <div className="flex space-x-2 mb-4">
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-1 rounded text-sm ${
                  viewMode === 'table'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                <Table className="w-4 h-4 inline mr-1" />
                جدول
              </button>
              <button
                onClick={() => setViewMode('chart')}
                className={`px-3 py-1 rounded text-sm ${
                  viewMode === 'chart'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                <BarChart3 className="w-4 h-4 inline mr-1" />
                رسم بياني
              </button>
              <button
                onClick={() => setViewMode('sql')}
                className={`px-3 py-1 rounded text-sm ${
                  viewMode === 'sql'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
              >
                <Code className="w-4 h-4 inline mr-1" />
                SQL
              </button>
            </div>

            {viewMode === 'table' && renderTable(message.data)}
            {viewMode === 'chart' && renderChart(message.data)}
            {viewMode === 'sql' && (
              <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-x-auto">
                <code className="text-gray-800 dark:text-gray-200">{message.sql}</code>
              </pre>
            )}
          </>
        )}
      </div>
    );
  };

  const quickSuggestions = [
    { text: 'أعرض لي أكثر 5 منتجات مبيعاً', icon: TrendingUp },
    { text: 'قارن بين مبيعات هذا الشهر والشهر الماضي', icon: BarChart3 },
    { text: 'ما هي أفضل الفروع أداءً؟', icon: Bot },
    { text: 'أكثر العملاء شراءً في الربع الأخير', icon: User }
  ];

  return (
    <div className="flex flex-col h-[calc(100vh-160px)] bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 w-full max-w-none">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center mr-4">
              <Bot className="w-7 h-7 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                وكيل الذكاء الاصطناعي - دردشة موحدة
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                اسأل أي سؤال عن بياناتك واحصل على تحليلات فورية
              </p>
            </div>
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {messages.filter(m => m.type === 'result').length} استعلام منفذ
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {showWelcome && (
          <div className="text-center py-12">
            <div className="max-w-4xl mx-auto">
              <Bot className="w-20 h-20 text-blue-600 mx-auto mb-6" />
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                مرحباً بك في وكيل الذكاء الاصطناعي!
              </h3>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
                اسأل أي سؤال عن بياناتك وسأقوم بتحليلها وعرض النتائج بطريقة تفاعلية
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-6xl mx-auto">
                {quickSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => setInputValue(suggestion.text)}
                    className="group p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 transition-all duration-300 text-blue-700 dark:text-blue-300 text-right border border-blue-200 dark:border-blue-800 hover:shadow-lg hover:scale-105"
                  >
                    <div className="flex items-center mb-3">
                      <suggestion.icon className="w-6 h-6 mr-3 group-hover:scale-110 transition-transform text-blue-600 dark:text-blue-400" />
                      <span className="font-semibold text-sm">اقتراح سريع</span>
                    </div>
                    <p className="text-sm leading-relaxed">{suggestion.text}</p>
                  </button>
                ))}
              </div>

              <div className="mt-8 flex items-center justify-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  <span>تحليل ذكي</span>
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  <span>رسوم بيانية</span>
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                  <span>دردشة تفاعلية</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            {message.type === 'result' ? (
              <div className="w-full">
                <ResultMessage message={message} />
              </div>
            ) : (
              <div
                className={`max-w-[85%] lg:max-w-[70%] rounded-lg p-3 ${
                  message.type === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                }`}
              >
                <div className="flex items-start">
                  {message.type === 'assistant' && (
                    <Bot className="w-4 h-4 mr-2 mt-0.5 text-blue-600 dark:text-blue-400" />
                  )}
                  {message.type === 'user' && (
                    <User className="w-4 h-4 mr-2 mt-0.5" />
                  )}
                  <div className="whitespace-pre-wrap text-sm">{message.content}</div>
                </div>
              </div>
            )}
          </div>
        ))}

        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
              <div className="flex items-center">
                <Loader2 className="w-4 h-4 animate-spin mr-2 text-blue-600" />
                <span className="text-sm text-gray-600 dark:text-gray-400">جاري التحليل...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
        {/* مؤشر الحالة */}
        {isLoading && (
          <div className="mb-3 flex items-center text-sm text-blue-600 dark:text-blue-400">
            <Loader2 className="w-4 h-4 animate-spin mr-2" />
            <span>جاري تحليل الاستعلام وجلب البيانات...</span>
          </div>
        )}

        <div className="flex gap-3 max-w-none">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={messages.length === 0 ? "اكتب استعلامك هنا... مثل: أعرض لي أكثر 5 منتجات مبيعاً" : "اسأل سؤالاً عن النتائج أو اكتب استعلاماً جديداً..."}
            className="flex-1 px-6 py-4 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-right text-lg"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:shadow-none"
          >
            {isLoading ? (
              <Loader2 className="w-6 h-6 animate-spin" />
            ) : (
              <Send className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* نصائح سريعة */}
        {!isLoading && inputValue.length === 0 && messages.length > 0 && (
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 text-center">
            💡 يمكنك طرح أسئلة عن النتائج أو كتابة استعلام جديد
          </div>
        )}
      </div>
    </div>
  );
}
