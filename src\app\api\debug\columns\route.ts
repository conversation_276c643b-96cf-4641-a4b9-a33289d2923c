import { NextRequest, NextResponse } from 'next/server';
import sql from 'mssql';

export async function POST(request: NextRequest) {
  let pool: sql.ConnectionPool | null = null;
  
  try {
    const { connectionData } = await request.json();
    
    const config = {
      server: connectionData.server,
      database: connectionData.database,
      user: connectionData.username,
      password: connectionData.password,
      port: parseInt(connectionData.port),
      options: {
        encrypt: false,
        trustServerCertificate: connectionData.trustServerCertificate || true,
        enableArithAbort: true
      }
    };

    console.log('🔗 محاولة الاتصال لفحص الأعمدة...');
    pool = await sql.connect(config);
    
    // فحص الأعمدة في tbltemp_ItemsMain
    const columnsQuery = `
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'tbltemp_ItemsMain'
      ORDER BY ORDINAL_POSITION
    `;
    
    const columnsResult = await pool.request().query(columnsQuery);
    
    // اختبار استعلام بسيط
    const testQuery = `SELECT TOP 3 * FROM tbltemp_ItemsMain`;
    const testResult = await pool.request().query(testQuery);
    
    const sampleColumns = testResult.recordset.length > 0 
      ? Object.keys(testResult.recordset[0]) 
      : [];
    
    await pool.close();
    
    return NextResponse.json({
      success: true,
      schemaColumns: columnsResult.recordset,
      sampleColumns,
      sampleData: testResult.recordset.slice(0, 2) // عينة صغيرة
    });
    
  } catch (error: any) {
    console.error('❌ خطأ في فحص الأعمدة:', error);
    
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {
        console.error('خطأ في إغلاق الاتصال:', closeError);
      }
    }
    
    return NextResponse.json({
      success: false,
      error: error.message,
      details: error.code || 'UNKNOWN_ERROR'
    }, { status: 500 });
  }
}
