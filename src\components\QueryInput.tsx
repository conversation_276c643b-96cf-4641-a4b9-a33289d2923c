'use client';

import { useState, useRef } from 'react';
import { Send, Loader2, Mi<PERSON>, Mic<PERSON>ff } from 'lucide-react';

interface QueryInputProps {
  onSubmit: (query: string) => void;
  isProcessing: boolean;
  placeholder?: string;
}

export default function QueryInput({ onSubmit, isProcessing, placeholder }: QueryInputProps) {
  const [query, setQuery] = useState('');
  const [isListening, setIsListening] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim() && !isProcessing) {
      onSubmit(query.trim());
      setQuery('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setQuery(e.target.value);
    adjustTextareaHeight();
  };

  // Voice input functionality (placeholder for future implementation)
  const toggleVoiceInput = () => {
    setIsListening(!isListening);
    // TODO: Implement speech recognition
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
      <form onSubmit={handleSubmit} className="p-4">
        <div className="flex items-start space-x-4">
          <div className="flex-1">
            <textarea
              ref={textareaRef}
              value={query}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder={placeholder || 'اكتب استعلامك هنا...'}
              className="w-full resize-none border-0 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-0 focus:outline-none text-lg"
              rows={1}
              style={{ minHeight: '24px', maxHeight: '200px' }}
              disabled={isProcessing}
            />
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Voice Input Button */}
            <button
              type="button"
              onClick={toggleVoiceInput}
              disabled={isProcessing}
              className={`p-2 rounded-lg transition-colors ${
                isListening
                  ? 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600'
              } disabled:opacity-50`}
            >
              {isListening ? (
                <MicOff className="w-5 h-5" />
              ) : (
                <Mic className="w-5 h-5" />
              )}
            </button>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={!query.trim() || isProcessing}
              className="p-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:shadow-none"
            >
              {isProcessing ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Send className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>

        {/* Processing Indicator */}
        {isProcessing && (
          <div className="mt-4 flex items-center text-sm text-gray-600 dark:text-gray-400">
            <Loader2 className="w-4 h-4 animate-spin mr-2" />
            <span>جاري معالجة الاستعلام وتحليل البيانات...</span>
          </div>
        )}

        {/* Voice Input Indicator */}
        {isListening && (
          <div className="mt-4 flex items-center text-sm text-red-600 dark:text-red-400">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></div>
            <span>جاري الاستماع... تحدث الآن</span>
          </div>
        )}
      </form>

      {/* Quick Actions */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex flex-wrap gap-2">
          {[
            'أعرض لي أكثر المنتجات مبيعاً',
            'مقارنة الفروع',
            'تحليل العملاء',
            'اتجاهات المبيعات'
          ].map((suggestion, index) => (
            <button
              key={index}
              onClick={() => setQuery(suggestion)}
              disabled={isProcessing}
              className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
            >
              {suggestion}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
