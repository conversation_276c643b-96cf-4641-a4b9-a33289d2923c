[{"intent": "get_top_selling_products", "description": "عرض أكثر المنتجات مبيعاً خلال فترة معينة", "keywords": ["ما هو أكثر منتج تم بيعه هذا الأسبوع؟", "أكثر منتج بعناه هذا الشهر", "ايش أكثر صنف طلبوه العملاء؟", "افضل المنتجات مبيعاً في آخر 7 أيام", "اش أكثر حاجة مشت؟", "أيش أعلى منتج مبيع؟", "المنتجات الأكثر طلباً هذا الشهر", "ايش المنتجات اللي راحت بكثر؟", "أفضل مبيعات الأسبوع", "ما هو المنتج الأكثر مبيعاً؟", "ايش الصنف اللي طلبوا منه أكثر؟", "أعلى منتجات مبيعات اليوم", "المنتجات الرائدة في المبيعات"], "entities": ["TheDate", "BranchName", "ItemName"], "relatedTables": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"]}, {"intent": "get_top_customers", "description": "عرض أكثر العملاء شراءً", "keywords": ["مين أكثر عميل اشترى؟", "أكثر زبون تعامل معنا", "من هو أفضل عميل خلال هذا الشهر؟", "اش العميل اللي طلب كثير؟", "أكثر الزبائن صرفاً", "من اشتراهم أكثر؟", "أفضل العملاء هذا الشهر", "العملاء الأكثر إنفاقاً", "من الزبائن اللي صرفوا أكتر؟", "من هو العميل الأكثر ولاءً؟", "أعلى عملاء إنفاقاً", "العملاء المميزون"], "entities": ["TheDate", "ClientName", "BranchName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_sales_report", "description": "تقرير المبيعات حسب الفرع أو التاريخ", "keywords": ["اريد تقرير المبيعات", "كم بعنا هذا الشهر؟", "كم مبيعات فرع صنعاء؟", "المبيعات خلال الأسبوع", "تقرير البيع لفرع عدن", "كم بعنا اليوم؟", "مبيعات الشهر الماضي", "تقرير المبيعات الشهري", "كم بعنا بالأمس؟", "إيرادات اليوم", "تقرير المبيعات اليومي", "مبيعات هذا الأسبوع"], "entities": ["TheDate", "BranchName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_purchase_report", "description": "تقرير المشتريات حسب المورد أو الفرع", "keywords": ["كم اشترينا هذا الشهر؟", "المشتريات من المورد فلان", "اش اشترينا لفرع إب؟", "تقرير مشتريات الأسبوع", "كم طلبنا من المورد هذا؟", "المشتريات الشهرية", "كم اشترينا من المورد XYZ؟", "المشتريات حسب التاريخ", "تقرير الشراء الشهري", "فواتير الشراء", "المشتريات من الموردين", "تقرير المشتريات"], "entities": ["TheDate", "DistributorName", "BranchName"], "relatedTables": ["tbltemp_Inv_MainInvoice"]}, {"intent": "get_returned_items", "description": "عرض المنتجات المرجعة حسب الفرع أو التاريخ", "keywords": ["ايش أكثر منتج رجعوه؟", "المرتجعات الأسبوع الماضي", "المنتجات اللي رجعوها الناس", "اش رجعوا العملاء؟", "كم مرتجعات عندنا؟", "ايش المنتجات المرتجعة؟", "المرتجعات هذا الشهر", "كم منتج رجع؟", "المنتجات اللي رجعت", "المرتجعات اليومية", "كم المنتجات المرتجعة؟", "المنتجات المعيبة"], "entities": ["TheDate", "ItemName", "BranchName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_product_stock_quantity", "description": "عرض رصيد المخزون لمنتج معين", "keywords": ["كم كمية كولا عندنا؟", "كم موجود من فانتا؟", "كم رصيد المنتج 100؟", "كم متوفر من هذا المنتج؟", "كم الكمية في المخزن؟", "كم منتج XYZ لدينا؟", "كم باقي من هذا الصنف؟", "كم الكمية المتاحة؟", "كم الكمية الفعلية؟", "كم المخزون الحالي؟", "الكمية القابلة للبيع", "الر<PERSON>ي<PERSON> المتاح"], "entities": ["ItemName", "BranchName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_product_details", "description": "عرض تفاصيل منتج معين", "keywords": ["ما معلومات كولا؟", "تفاصيل المنتج 100", "عرض معلومات فانتا", "بيانات هذا المنتج", "ما مواصفات هذا الصنف؟", "معلومات المنتج كاملة", "عرض تفاصيل المنتج", "ما سعر هذا المنتج؟", "كم سعر المنتج؟", "ما الباركود؟", "ما تصنيف المنتج؟"], "entities": ["ItemName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_invoice_details", "description": "عرض تفاصيل فاتورة معينة", "keywords": ["ما محتوى الفاتورة 1001؟", "عرض فاتورة رقم 12345", "تفاصيل الفاتورة الأخيرة", "فاتورة العميل محمد", "عرض الفاتورة الإلكترونية", "ما في فاتورة 500؟", "طباعة فاتورة 1000", "الفاتورة بتاريخ كذا", "تفاصيل الفاتورة برقم", "فاتورة المبيعات", "الفاتورة الكاملة"], "entities": ["TheNumber", "TheDate", "ClientName"], "relatedTables": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"]}, {"intent": "get_low_stock_products", "description": "عرض المنتجات ذات الكمية المنخفضة", "keywords": ["ايش المنتجات قليلة الكمية؟", "كم المنتجات ناقصة؟", "ايش الصنف اللي قاعد يخلص؟", "المنتجات تحت الحد الأدنى", "ايش الحاجات اللي خلت؟", "المنتجات قربت تنفذ", "كم المنتجات عندهم كمية قليلة؟", "المنتجات اللي تحتاج إعادة طلب", "المنتجات النادرة", "المنتجات تحت مستوى الأمان", "تنبيه نفاد المخزون", "المنتجات المطلوب طلبها"], "entities": ["BranchName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_expired_products", "description": "عرض المنتجات المنتهية الصلاحية", "keywords": ["ايش المنتجات المنتهية الصلاحية؟", "المنتجات اللي خلت الصلاحية", "ايش الحاجات اللي انتهت؟", "المنتجات تالفة الصلاحية", "كم منتج منتهي الصلاحية؟", "ايش المنتجات اللي ما تصلح؟", "المنتجات اللي لازم نتخلص منها", "ايش الصنف اللي خلت مدة صلاحيته؟", "المنتجات الفاسدة", "المنتجات غير الصالحة", "المنتجات التي يجب التخلص منها", "تنبيه انتهاء الصلاحية"], "entities": ["TheDate", "BranchName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_branch_performance", "description": "عرض أداء الفرع من حيث المبيعات", "keywords": ["كم أداء فرع صنعاء؟", "ايش كفاءة الفرع؟", "كم إيرادات الفرع؟", "أداء الفرع هذا الشهر", "كم بيع فرع عدن؟", "مقارنة أداء الفروع", "كم فرع إب بيع؟", "أداء المبيعات بالفرع", "كم الإيرادات اليومية؟", "أداء المبيعات الشهرية", "مقارنة الفروع في المبيعات", "أفضل فرع مبيعاً"], "entities": ["BranchName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_client_credit_status", "description": "عرض حالة الائتمان للعميل", "keywords": ["كم ديون العميل محمد؟", "حالة الائتمان للزبون", "كم رصيد العميل؟", "هل العميل عنده دين؟", "كم الباقي للعميل؟", "الوضع المالي للزبون", "هل يمكن للعميل شراء بقية؟", "كم يمكن للعميل يشتري؟", "الح<PERSON> الائتماني للعميل", "الدين المستحق للعميل", "الرصيد الائتماني", "العميل على ذمته كم؟"], "entities": ["ClientName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_product_profit_margin", "description": "عرض هامش الربح للمنتج", "keywords": ["كم ربح المنتج كولا؟", "ما هامش الربح؟", "كم الربحية لهذا الصنف؟", "تحليل الربح للمنتج", "كم ربحنا من هذا المنتج؟", "الربح الصافي للمنتج", "كم الربح بالنسبة للسعر؟", "تحليل تكلفة وربح المنتج", "ربحية المنتج بالتفصيل", "الربح لكل وحدة", "تحليل الربحية الشاملة", "هامش الربح المئوي"], "entities": ["ItemName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_supplier_invoices", "description": "عرض فواتير الشراء من مورد معين", "keywords": ["ما فواتير المورد XYZ؟", "فواتير الشراء من المورد", "كم اشترينا من هذا المورد؟", "فواتير المورد هذا الشهر", "عرض فواتير الموردين", "فواتير الشراء الأخيرة", "فواتير المو<PERSON>د أحمد", "فواتير المشتريات من المورد", "فواتير الموردين المعلقة", "فواتير الشراء المدفوعة", "فواتير الموردين حسب التاريخ", "تقرير فواتير الموردين"], "entities": ["DistributorName", "TheDate"], "relatedTables": ["tbltemp_Inv_MainInvoice"]}, {"intent": "get_product_movement", "description": "عرض حركة منتج معين في المخازن", "keywords": ["ايش حركة كولا؟", "سجل دخول وخروج المنتج", "حركة المخزون للصنف", "كم دخل وخرج من هذا المنتج؟", "حركة المنتج هذا الشهر", "سجل الاستلام والتسليم", "حركة المخزون الشاملة", "كم منتج XYZ دخل وخرج؟", "حركة الأصناف", "سجل المعاملات", "تقرير حركة المخزون", "العمليات على المنتج"], "entities": ["ItemName", "TheDate", "BranchName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_branch_inventory", "description": "عرض المخزون في فرع معين", "keywords": ["كم مخزون فرع صنعاء؟", "عرض محتوى المخزن", "المنتجات في الفرع", "كم موجود في المخزن؟", "تقرير مخزون الفرع", "المخزون حسب الفرع", "كم منتج في فرع عدن؟", "عرض المخزون بالفرع", "<PERSON><PERSON><PERSON> الم<PERSON>زن", "تقرير الجرد الشهري", "المخزون الحالي", "المنتجات المتوفرة"], "entities": ["BranchName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_product_barcode", "description": "عرض الباركود لمنتج معين", "keywords": ["ما باركود كولا؟", "ر<PERSON>ز الباركود للمنتج", "الباركود الخاص بالفانتا", "ر<PERSON>ز المنتج 100", "عرض الباركود للطباعة", "الباركود للمنتج", "ر<PERSON>ز الباركود للتغليف", "الباركود لل.scan", "طباعة باركود المنتج", "الباركود للبيع", "<PERSON><PERSON><PERSON> الباركود الرقمي", "الباركود التجاري"], "entities": ["ItemName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_product_reorder_point", "description": "عرض نقطة إعادة الطلب لمنتج", "keywords": ["كم نقطة إعادة الطلب لكولا؟", "متى نطلب هذا المنتج؟", "كم الحد الأدنى للمخزون؟", "نقطة التنبيه للمنتج", "كم الكمية الحرجة؟", "متى يطلب المنتج مجدداً؟", "كم مستوى المخزون الآمن؟", "نقطة الطلب الآمنة", "مستوى التنبيه للمخزون", "الح<PERSON> الأدنى لطلب المنتج", "نقطة الطلب الحرجة", "المستوى الآمن للمخزون"], "entities": ["ItemName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_client_sales_history", "description": "عرض تاريخ المبيعات لعميل معين", "keywords": ["ما تاريخ مبيعات العميل محمد؟", "سجل مشتريات الزبون", "كم اشترا العميل؟", "تاريخ الشراء للعميل", "فواتير العميل السابقة", "سجل العمليات للعميل", "كم العميل صرف هذا الشهر؟", "مشتريات العميل حسب التاريخ", "سجل المبيعات السنوي", "تاريخ الفواتير للعميل", "العميل ومشترياته", "تقرير العميل الشامل"], "entities": ["ClientName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "compare_products_sales", "description": "مقارنة مبيعات منتجين أو أكثر", "keywords": ["قارن مبيعات كولا وفانتا", "ما أفضل منتج بين هذين؟", "أيهما أكثر مبيعاً كولا أم سبريت؟", "مقارنة مبيعات المنتجين", "تحليل أداء المنتجين", "أيهما أكثر ربحاً؟", "مقارنة الكمية المباعة", "تحليل المنتجين التنافسيين", "أيهما أكثر طلباً؟", "مقارنة الأداء التجاري", "تحليل مبيعات المنتجين", "أيهما أكثر شعبية؟"], "entities": ["ItemName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "compare_branches_performance", "description": "مقارنة أداء فرعين أو أكثر", "keywords": ["قارن أداء فرع صنعاء وعدن", "أيهما أفضل فرع؟", "ما الفرع الأكثر مبيعاً؟", "مقارنة إيرادات الفرعين", "تحليل أداء الفروع", "أيهما أكثر ربحاً؟", "مقارنة المبيعات بين الفروع", "تحليل الفروع التنافسية", "أيهما أكثر نشاطاً؟", "مقارنة الأداء التجاري للفروع", "تحليل مبيعات الفروع", "أيهما أكثر كفاءة؟"], "entities": ["BranchName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "compare_clients_purchases", "description": "مقارنة مشتريات عميلين أو أكثر", "keywords": ["قارن مشتريات العميل أحمد وعلي", "أيهما أكثر إنفاقاً؟", "ما العميل الأكثر ولاءً؟", "مقارنة إنفاق العملاء", "تحليل أداء العملاء", "أيهما أكثر شراءً؟", "مقارنة القيمة المشتراة", "تحليل العملاء التنافسيين", "أيهما أكثر نشاطاً؟", "مقارنة الأداء التجاري للعملاء", "تحليل مشتريات العملاء", "أيهما أكثر أهمية؟"], "entities": ["ClientName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_product_sales_trend", "description": "عرض اتجاه مبيعات منتج معين عبر الزمن", "keywords": ["ما اتجاه مبيعات كولا؟", "تحليل مبيعات المنتج 100", "الاتجاه التجاري لفانتا", "نمو مبيعات هذا المنتج", "الاتجاه الشهري للمنتج", "تحليل أداء المبيعات", "الاتجاه الموسمي للمنتج", "مبيعات المنتج عبر الزمن", "النمو في مبيعات المنتج", "تحليل الاتجاه التجاري", "الاتجاهات الموسمية", "النمو المئوي للمنتج"], "entities": ["ItemName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_branch_sales_trend", "description": "عرض اتجاه مبيعات فرع معين عبر الزمن", "keywords": ["ما اتجاه مبيعات فرع صنعاء؟", "تحليل مبيعات الفرع الأول", "الاتجاه التجاري لفرع عدن", "نمو مبيعات هذا الفرع", "الاتجاه الشهري للفرع", "تحليل أداء مبيعات الفرع", "الاتجاه الموسمي للفرع", "مبيعات الفرع عبر الزمن", "النمو في مبيعات الفرع", "تحليل الاتجاه التجاري للفرع", "الاتجاهات الموسمية للفرع", "النمو المئوي للفرع"], "entities": ["BranchName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_client_purchase_trend", "description": "عرض اتجاه مشتريات عميل معين عبر الزمن", "keywords": ["ما اتجاه مشتريات العميل محمد؟", "تحليل إنفاق العميل أحمد", "الاتجاه التجاري للزبون", "نمو إنفاق هذا العميل", "الاتجاه الشهري للعميل", "تحليل أداء إنفاق العميل", "الاتجاه الموسمي للعميل", "إنفاق العميل عبر الزمن", "النمو في إنفاق العميل", "تحليل الاتجاه التجاري للعميل", "الاتجاهات الموسمية للعميل", "النمو المئوي للعميل"], "entities": ["ClientName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_monthly_sales_report", "description": "تقرير المبيعات الشهري", "keywords": ["تقرير مبيعات شهر يناير", "أداء المبيعات لشهر فبراير", "الإيرادات الشهرية لمارس", "عرض تقرير المبيعات الشهري", "تقرير المبيعات لشهر أبريل", "أداء المبيعات هذا الشهر", "تقرير الإيرادات الشهرية", "المبيعات الشهرية بالتفصيل", "تقرير المبيعات حسب الشهر", "تحليل المبيعات الشهري", "تقرير المبيعات الشهري الكامل", "الإيرادات الشهرية"], "entities": ["TheDate", "BranchName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_yearly_sales_report", "description": "تقرير المبيعات السنوي", "keywords": ["تقرير مبيعات عام 2024", "أداء المبيعات للعام الماضي", "الإيرادات السنوية لعام 2023", "عرض تقرير المبيعات السنوي", "تقرير المبيعات لعام 2024", "أداء المبيعات هذا العام", "تقرير الإيرادات السنوية", "المبيعات السنوية بالتفصيل", "تقرير المبيعات حسب السنة", "تحليل المبيعات السنوي", "تقرير المبيعات السنوي الكامل", "الإيرادات السنوية"], "entities": ["TheDate", "BranchName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_daily_sales_report", "description": "تقرير المبيعات اليومي", "keywords": ["تقرير مبيعات اليوم", "أداء المبيعات اليومي", "الإيرادات اليومية", "عرض تقرير المبيعات اليومي", "تقرير المبيعات لهذا اليوم", "أداء المبيعات اليوم", "تقرير الإيرادات اليومية", "المبيعات اليومية بالتفصيل", "تقرير المبيعات حسب اليوم", "تحليل المبيعات اليومي", "تقرير المبيعات اليومي الكامل", "الإيرادات اليوم"], "entities": ["TheDate", "BranchName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_product_category_sales", "description": "تقرير مبيعات حسب تصنيف المنتج", "keywords": ["كم مبيعات فئة المشروبات؟", "مبيعات التصنيف الغذائي", "تقرير المبيعات حسب الفئة", "أداء فئات المنتجات", "تحليل مبيعات التصنيفات", "كم مبيعات فئة الأدوية؟", "مبيعات فئة التنظيف", "تقرير الفئات التجارية", "تحليل أداء التصنيفات", "أفضل فئات مبيعاً", "مبيعات الفئات المختلفة", "تقرير التصنيفات"], "entities": ["CategoryName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_branch_category_sales", "description": "تقرير مبيعات الفرع حسب تصنيف المنتج", "keywords": ["كم مبيعات فرع صنعاء حسب الفئة؟", "مبيعات الفرع الأول حسب التصنيف", "تقرير فرع عدن حسب الفئة", "أداء فرع إب في التصنيفات", "تحليل مبيعات الفرع بالتصنيف", "كم مبيعات فرع حضرموت حسب الفئة؟", "مبيعات فرع تعز حسب التصنيف", "تقرير الفرع حسب الفئات", "تحليل أداء الفرع بالتصنيف", "أفضل فئات فرع صنعاء", "مبيعات الفرع بالتصنيفات", "تقرير الفرع والتصنيف"], "entities": ["BranchName", "CategoryName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_client_category_purchases", "description": "تقرير مشتريات العميل حسب تصنيف المنتج", "keywords": ["كم اشترا العميل محمد حسب الفئة؟", "مشتريات العميل أحمد حسب التصنيف", "تقرير العميل علي حسب الفئة", "أداء العميل في التصنيفات", "تحليل مشتريات العميل بالتصنيف", "كم اشترا العميل حسب الفئة؟", "مشتريات العميل حسب التصنيف", "تقرير العميل حسب الفئات", "تحليل أداء العميل بالتصنيف", "أفضل فئات العميل محمد", "مشتريات العميل بالتصنيفات", "تقرير العميل والتصنيف"], "entities": ["ClientName", "CategoryName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_product_price_analysis", "description": "تحليل أسعار المنتجات", "keywords": ["ما سعر المنتج كولا؟", "تحليل أسعار المنتج 100", "مقارنة أسعار المنتجين", "سعر المنتج بالجملة", "سعر المنتج للمستهلك", "تحليل الأسعار التجارية", "سعر المنتج حسب التاريخ", "مقارنة الأسعار بالسوق", "تحليل الأسعار التنافسية", "سعر المنتج الحالي", "قائمة الأسعار الحالية", "تحليل الأسعار الشامل"], "entities": ["ItemName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_branch_expense_analysis", "description": "تحليل مصاريف الفرع", "keywords": ["كم مصاريف فرع صنعاء؟", "تحليل نفقات الفرع الأول", "مصاريف فرع عدن الشهرية", "تقرير مصاريف الفرع", "تحليل النفقات التجارية", "مصاريف الفرع حسب التاريخ", "مقارنة مصاريف الفروع", "تحليل المصاريف التنافسية", "مصاريف الفرع التشغيلية", "مصاريف الفرع الحالية", "قائمة المصاريف الحالية", "تحليل المصاريف الشامل"], "entities": ["BranchName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_client_payment_analysis", "description": "تحليل مدفوعات العميل", "keywords": ["كم مدفوعات العميل محمد؟", "تحليل دفعات العميل أحمد", "مدفوعات العميل علي الشهرية", "تقرير مدفوعات العميل", "تحليل الدفعات التجارية", "مدفوعات العميل حسب التاريخ", "مقارنة مدفوعات العملاء", "تحليل المدفوعات التنافسية", "مدفوعات العميل النقدية", "مدفوعات العميل الحالية", "قائمة المدفوعات الحالية", "تحليل المدفوعات الشامل"], "entities": ["ClientName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_supplier_payment_analysis", "description": "تحليل مدفوعات المورد", "keywords": ["كم مدفوعات المورد XYZ؟", "تحليل دفعات المو<PERSON>د أحمد", "مدفوعات المورد علي الشهرية", "تقرير مدفوعات المورد", "تحليل الدفعات التجارية", "مدفوعات المورد حسب التاريخ", "مقارنة مدفوعات الموردين", "تحليل المدفوعات التنافسية", "مدفوعات المورد النقدية", "مدفوعات المورد الحالية", "قائمة المدفوعات الحالية", "تحليل المدفوعات الشامل"], "entities": ["DistributorName", "TheDate"], "relatedTables": ["tbltemp_Inv_MainInvoice"]}, {"intent": "get_inventory_turnover_analysis", "description": "تحليل معدل دوران المخزون", "keywords": ["كم معدل دوران كولا؟", "تحليل دوران المنتج 100", "معدل التدوير التجاري", "دوران المخزون في الفرع", "تحليل كفاءة المخزون", "معدل دوران المخازن", "تحليل التدوير التجاري", "دوران المخزون الشهري", "معدل التدوير السنوي", "دوران المخزون الحالي", "تحليل دوران المخزون الشامل", "معدل التدوير الكامل"], "entities": ["ItemName", "BranchName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_product_demand_forecast", "description": "توقع الطلب على منتج معين", "keywords": ["كم طلب كولا الشهر القادم؟", "توقع الطلب للمنتج 100", "تحليل الطلب المستقبلي", "توقع المبيعات التجارية", "طلب المنتج حسب التاريخ", "مقارنة الطلب المتوقع", "تحليل الطلب التنافسي", "طلب المنتج المستقبلي", "طلب المنتج الحالي", "قائمة الطلبات المتوقعة", "تحليل الطلب الشامل", "توقع الطلب الشهري"], "entities": ["ItemName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_branch_demand_forecast", "description": "توقع الطلب على فرع معين", "keywords": ["كم طلب فرع صنعاء الشهر القادم؟", "توقع الطلب للفرع الأول", "تحليل الطلب المستقبلي للفرع", "توقع مبيعات الفرع التجاري", "طلب الفرع حسب التاريخ", "مقارنة الطلب المتوقع للفروع", "تحليل الطلب التنافسي للفرع", "طلب الفرع المستقبلي", "طلب الفرع الحالي", "قائمة طلبات الفرع المتوقعة", "تحليل الطلب الشامل للفرع", "توقع الطلب الشهري للفرع"], "entities": ["BranchName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_client_demand_forecast", "description": "توقع الطلب من عميل معين", "keywords": ["كم طلب العميل محمد الشهر القادم؟", "توقع الطلب للعميل أحمد", "تحليل الطلب المستقبلي للعميل", "توقع مشتريات العميل التجاري", "طلب العميل حسب التاريخ", "مقارنة الطلب المتوقع للعملاء", "تحليل الطلب التنافسي للعميل", "طلب العميل المستقبلي", "طلب العميل الحالي", "قائمة طلبات العميل المتوقعة", "تحليل الطلب الشامل للعميل", "توقع الطلب الشهري للعميل"], "entities": ["ClientName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_seasonal_trend_analysis", "description": "تحليل الاتجاهات الموسمية", "keywords": ["ما الاتجاهات الموسمية في المبيعات؟", "المواسم التجارية لهذا العام", "الاتجاهات الزمنية للمنتجات", "المواسم ذات الطلب العالي", "عرض الاتجاهات الدورية", "المواسم التي تزيد فيها المبيعات", "تحليل الاتجاهات الموسمية", "المنتجات حسب الموسم", "الاتجاهات الموسمية للعام الماضي", "المواسم التجارية الناجحة", "تحليل المواسم التجارية", "الاتجاهات الموسمية الشاملة"], "entities": ["TheDate", "ItemName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_promotional_effect_analysis", "description": "تحليل تأثير العروض الترويجية", "keywords": ["كم زادت المبيعات بالعرض؟", "تحليل تأثير الحملة الترويجية", "العروض التجارية لهذا الشهر", "تأثير الخصومات على المبيعات", "عرض الاتجاهات الترويجية", "العروض التي زادت المبيعات", "تحليل تأثير العروض", "المنتجات حسب العرض", "تأثير العروض للعام الماضي", "العروض التجارية الناجحة", "تحليل العروض التجارية", "تأثير العروض الشامل"], "entities": ["TheDate", "ItemName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_customer_satisfaction_analysis", "description": "تحليل رضا العملاء", "keywords": ["كم رضا العملاء عن كولا؟", "تحليل رضا العملاء للمنتج 100", "تقييمات العملاء التجارية", "رضا العملاء حسب التاريخ", "عرض التقييمات الإيجابية", "المنتجات ذات الرضا العالي", "تحليل رضا العملاء", "المنتجات حسب التقييم", "رضا العملاء للعام الماضي", "المنتجات التجارية الناجحة", "تحليل رضا العملاء الشامل", "تقييمات العملاء الشاملة"], "entities": ["ClientName", "ItemName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_competitor_analysis", "description": "تحليل المنافسة في السوق", "keywords": ["ما المنتجات المنافسة لكولا؟", "المنافسون في السوق", "المنافسة التجارية للمنتج", "تحليل السوق التنافسي", "المنتجات المماثلة", "عرض المنافسون الرئيسيون", "المنافسة حسب السعر", "تحليل المنافسة في السوق", "المنتجات المنافسة حسب النوع", "مقارنة المنتجات المنافسة", "تحليل المنافسة الشامل", "المنافسة التجارية الشاملة"], "entities": ["ItemName"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_market_share_analysis", "description": "تحليل حصة السوق", "keywords": ["كم حصة السوق لكولا؟", "تحليل حصة السوق للمنتج 100", "الحصة السوقية التجارية", "حصة السوق حسب التاريخ", "عرض الحصة السوقية", "المنتجات ذات الحصة الأعلى", "تحليل الحصة السوقية", "المنتجات حسب الحصة", "حصة السوق للعام الماضي", "المنتجات التجارية الناجحة", "تحليل الحصة السوقية الشامل", "الحصة السوقية الشاملة"], "entities": ["ItemName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_financial_performance_analysis", "description": "تحليل الأداء المالي", "keywords": ["ما الأداء المالي للشركة؟", "تحليل الأداء المالي الشهري", "الأداء المالي التجاري", "الأداء المالي حسب التاريخ", "عرض الأداء المالي", "الفرع ذو الأداء الأعلى", "تحليل الأداء المالي", "الأداء المالي حسب الفرع", "الأداء المالي للعام الماضي", "الشركة التجارية الناجحة", "تحليل الأداء المالي الشامل", "الأداء المالي الشامل"], "entities": ["BranchName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_budget_vs_actual_analysis", "description": "تحليل الميزانية مقابل الفعلي", "keywords": ["قارن الميزانية الفعلية مع المخططة", "الإنفاق الفعلي لشهر يناير", "الميزانية المخططة للعام", "عرض مقارنة الميزانيات", "الإنفاق الفعلي حسب القسم", "تحليل الميزانية التشغيلية", "مقارنة الإنفاق الشهري", "الميزانية الفعلية للفرع", "تقرير الميزانية مقابل الفعلي", "الإنفاق مقابل الميزانية", "تحليل الميزانية الشامل", "الميزانية مقابل الفعلي الشامل"], "entities": ["BranchName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_key_performance_indicators", "description": "تحليل المؤشرات الرئيسية للأداء", "keywords": ["عرض المؤشرات الرئيسية للأداء", "KPIs للشركة هذا الشهر", "مؤشرات الأداء المؤسسي", "القياسات الأساسية للنشاط", "عرض KPIs حسب القسم", "تحليل المؤشرات الرئيسية", "مؤشرات الأداء الشهري", "KPIs حسب الفرع", "تقرير المؤشرات الأساسية", "القياسات المؤسسية", "تحليل KPIs الشامل", "المؤشرات الرئيسية الشاملة"], "entities": ["BranchName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_supply_chain_analysis", "description": "تحليل سلسلة التوريد", "keywords": ["ما حالة سلسلة التوريد؟", "تحليل سلسلة التوريد الشهري", "سلسلة التوريد التجارية", "سلسلة التوريد حسب التاريخ", "عرض سلسلة التوريد", "الموردون ذوو الأداء الأعلى", "تحليل سلسلة التوريد", "سلسلة التوريد حسب المورد", "سلسلة التوريد للعام الماضي", "الشركة التجارية الناجحة", "تحليل سلسلة التوريد الشامل", "سلسلة التوريد الشاملة"], "entities": ["DistributorName", "TheDate"], "relatedTables": ["tbltemp_Inv_MainInvoice"]}, {"intent": "get_quality_control_analysis", "description": "تحليل جودة المنتجات", "keywords": ["كم المنتجات المعيبة؟", "تحليل جودة المنتجات الشهري", "جودة المنتجات التجارية", "جودة المنتجات حسب التاريخ", "عرض جودة المنتجات", "المنتجات ذات الجودة الأعلى", "تحليل جودة المنتجات", "المنتجات حسب الجودة", "جودة المنتجات للعام الماضي", "المنتجات التجارية الناجحة", "تحليل جودة المنتجات الشامل", "جودة المنتجات الشاملة"], "entities": ["ItemName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_employee_performance_analysis", "description": "تحليل أداء الموظفين", "keywords": ["ما أداء الموظفين؟", "تحليل أداء الموظفين الشهري", "أداء الموظفين التجاري", "أداء الموظفين حسب التاريخ", "عرض أداء الموظفين", "الموظفون ذوو الأداء الأعلى", "تحليل أداء الموظفين", "أداء الموظفين حسب القسم", "أداء الموظفين للعام الماضي", "الشركة التجارية الناجحة", "تحليل أداء الموظفين الشامل", "أداء الموظفين الشامل"], "entities": ["BranchName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_customer_retention_analysis", "description": "تحليل احتفاظ العملاء", "keywords": ["كم احتفاظ العملاء؟", "تحليل احتفاظ العملاء الشهري", "احتفاظ العملاء التجاري", "احتفاظ العملاء حسب التاريخ", "عرض احتفاظ العملاء", "العملاء ذوو الاحتفاظ الأعلى", "تحليل احتفاظ العملاء", "احتفاظ العملاء حسب القسم", "احتفاظ العملاء للعام الماضي", "الشركة التجارية الناجحة", "تحليل احتفاظ العملاء الشامل", "احتفاظ العملاء الشامل"], "entities": ["ClientName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_product_lifecycle_analysis", "description": "تحليل دورة حياة المنتج", "keywords": ["ما دورة حياة كولا؟", "تحليل دورة حياة المنتج 100", "دورة حياة المنتج التجاري", "دورة حياة المنتج حسب التاريخ", "عرض دورة حياة المنتج", "المنتجات في مرحلة النمو", "تحليل دورة حياة المنتج", "المنتجات حسب المرحلة", "دورة حياة المنتج للعام الماضي", "المنتجات التجارية الناجحة", "تحليل دورة حياة المنتج الشامل", "دورة حياة المنتج الشاملة"], "entities": ["ItemName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}, {"intent": "get_innovation_opportunities_analysis", "description": "تحليل فرص الابتكار", "keywords": ["ما فرص الابتكار؟", "تحليل فرص الابتكار الشهري", "فرص الابتكار التجارية", "فرص الابتكار حسب التاريخ", "عرض فرص الابتكار", "الفرص ذات الإمكانية الأعلى", "تحليل فرص الابتكار", "فرص الابتكار حسب القسم", "فرص الابتكار للعام الماضي", "الشركة التجارية الناجحة", "تحليل فرص الابتكار الشامل", "فرص الابتكار الشاملة"], "entities": ["ItemName", "TheDate"], "relatedTables": ["tbltemp_ItemsMain"]}]