import { NextRequest, NextResponse } from 'next/server';
import sql from 'mssql';
import { analyzeQuery, generateSQL, analyzeResults } from '@/lib/ai-service';
import { findCachedQuery, addQueryToCache } from '@/lib/query-cache';

interface ConnectionData {
  server: string;
  database: string;
  username: string;
  password: string;
  port: string;
  trustServerCertificate: boolean;
}

export async function POST(request: NextRequest) {
  let pool: sql.ConnectionPool | null = null;
  const startTime = Date.now();

  try {
    const { query, connectionData }: { query: string; connectionData: ConnectionData } = await request.json();

    if (!query || !connectionData) {
      return NextResponse.json(
        { error: 'الاستعلام وبيانات الاتصال مطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من وجود الاستعلام في ذاكرة التخزين المؤقت
    const cachedResult = await findCachedQuery(query, connectionData);
    if (cachedResult) {
      console.log('تم العثور على الاستعلام في ذاكرة التخزين المؤقت');
      return NextResponse.json({
        success: true,
        data: cachedResult.data,
        sql: cachedResult.sql,
        analysis: cachedResult.analysis,
        cached: true,
        executionTime: cachedResult.executionTime,
        timestamp: cachedResult.timestamp
      });
    }

    // تحليل الاستعلام واستخراج النية والكيانات
    const queryAnalysis = await analyzeQuery(query);
    
    if (!queryAnalysis.success) {
      return NextResponse.json(
        { error: 'فشل في تحليل الاستعلام' },
        { status: 400 }
      );
    }

    // توليد استعلام SQL
    const sqlGeneration = await generateSQL(queryAnalysis);
    console.log('🔧 نتيجة توليد SQL:', {
      success: sqlGeneration.success,
      hasSql: !!sqlGeneration.sql,
      sqlLength: sqlGeneration.sql?.length || 0
    });

    if (!sqlGeneration.success || !sqlGeneration.sql) {
      console.error('❌ فشل في توليد SQL:', sqlGeneration);
      return NextResponse.json(
        { error: 'فشل في توليد استعلام SQL', details: sqlGeneration },
        { status: 400 }
      );
    }

    // إعداد الاتصال بقاعدة البيانات
    const config: sql.config = {
      server: connectionData.server,
      database: connectionData.database,
      port: parseInt(connectionData.port) || 1433,
      options: {
        encrypt: true,
        trustServerCertificate: connectionData.trustServerCertificate,
        enableArithAbort: true,
      },
    };

    if (connectionData.username && connectionData.password) {
      config.user = connectionData.username;
      config.password = connectionData.password;
    } else {
      config.authentication = {
        type: 'ntlm',
        options: {
          domain: '',
          userName: '',
          password: '',
        },
      };
    }

    // تنفيذ الاستعلام
    pool = await sql.connect(config);
    const result = await pool.request().query(sqlGeneration.sql);
    
    // تحليل النتائج
    const analysis = await analyzeResults(query, result.recordset, queryAnalysis);

    // حساب وقت التنفيذ
    const endTime = Date.now();
    const executionTime = endTime - startTime;

    // حفظ النتائج في ذاكرة التخزين المؤقت
    try {
      await addQueryToCache(
        query,
        sqlGeneration.sql,
        result.recordset,
        analysis.insights,
        connectionData,
        executionTime
      );
      console.log('تم حفظ الاستعلام في ذاكرة التخزين المؤقت');
    } catch (cacheError) {
      console.error('خطأ في حفظ الاستعلام في ذاكرة التخزين المؤقت:', cacheError);
      // لا نوقف العملية بسبب خطأ في التخزين المؤقت
    }

    await pool.close();

    return NextResponse.json({
      success: true,
      query,
      sql: sqlGeneration.sql,
      data: result.recordset,
      analysis: analysis.insights,
      visualization: queryAnalysis.visualization,
      intent: queryAnalysis.intent,
      entities: queryAnalysis.entities,
      cached: false,
      executionTime,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('خطأ في معالجة الاستعلام:', error);
    
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {
        console.error('خطأ في إغلاق الاتصال:', closeError);
      }
    }

    let errorMessage = 'فشل في معالجة الاستعلام';
    
    if (error.message?.includes('Invalid object name')) {
      errorMessage = 'اسم الجدول أو العمود غير صحيح';
    } else if (error.message?.includes('Syntax error')) {
      errorMessage = 'خطأ في صيغة الاستعلام';
    } else if (error.message?.includes('Permission denied')) {
      errorMessage = 'ليس لديك صلاحية لتنفيذ هذا الاستعلام';
    } else if (error.message) {
      errorMessage = error.message;
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: error.code || 'UNKNOWN_ERROR'
      },
      { status: 500 }
    );
  }
}
