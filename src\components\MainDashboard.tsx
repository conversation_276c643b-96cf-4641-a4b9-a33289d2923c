'use client';

import { useState } from 'react';
import { Send, Database, BarChart3, Setting<PERSON>, TrendingUp, Bot } from 'lucide-react';
import QueryInput from './QueryInput';
import ResultsDisplay from './ResultsDisplay';
import CacheManager from './CacheManager';
import SettingsPanel from './SettingsPanel';
import PerformanceMonitor from './PerformanceMonitor';
import { useQuickNotifications } from './NotificationSystem';

interface MainDashboardProps {
  connectionData: any;
}

interface QueryResult {
  id: string;
  query: string;
  sql: string;
  data: any[];
  analysis: string;
  timestamp: Date;
  visualization?: string;
  success?: boolean;
  cached?: boolean;
}



export default function MainDashboard({ connectionData }: MainDashboardProps) {
  const [currentQuery, setCurrentQuery] = useState('');
  const [queryHistory, setQueryHistory] = useState<QueryResult[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState<'main' | 'cache'>('main');
  const [showSettings, setShowSettings] = useState(false);
  const [showPerformance, setShowPerformance] = useState(false);

  const notifications = useQuickNotifications();

  const handleQuerySubmit = async (query: string) => {
    if (!query.trim() || isProcessing) return;

    setIsProcessing(true);
    setCurrentQuery(query);

    try {
      // إرسال الاستعلام للمعالجة
      const response = await fetch('/api/query/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          connectionData
        }),
      });

      const result = await response.json();

      if (response.ok) {
        const newResult: QueryResult = {
          id: Date.now().toString(),
          query,
          sql: result.sql,
          data: result.data,
          analysis: result.analysis,
          timestamp: new Date(),
          visualization: result.visualization
        };

        setQueryHistory(prev => [newResult, ...prev]);
        // البقاء في الواجهة الرئيسية

        // إشعار نجاح
        notifications.success(
          'تم تنفيذ الاستعلام بنجاح',
          `تم العثور على ${result.data.length} صف من البيانات${result.cached ? ' (من التخزين المؤقت)' : ''}`
        );
      } else {
        notifications.error('خطأ في تنفيذ الاستعلام', result.error);
      }
    } catch (error) {
      console.error('خطأ في الشبكة:', error);
      notifications.error('فشل في معالجة الاستعلام', 'تحقق من الاتصال بالإنترنت وحاول مرة أخرى');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 shadow-lg border-b border-gray-200/50 dark:border-gray-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center mr-3">
                <Bot className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  وكيل الذكاء الاصطناعي
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  متصل بـ: {connectionData.database}@{connectionData.server}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowSettings(true)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title="الإعدادات"
              >
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 border-b border-gray-200/50 dark:border-gray-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('main')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'main'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Bot className="w-4 h-4 inline mr-2" />
              الواجهة الرئيسية
              {queryHistory.length > 0 && (
                <span className="mr-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-300">
                  {queryHistory.length}
                </span>
              )}
            </button>



            <button
              onClick={() => setActiveTab('cache')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'cache'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Database className="w-4 h-4 inline mr-2" />
              التخزين المؤقت
            </button>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'main' && (
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
            {/* الجانب الأيسر - منطقة الاستعلام والنتائج */}
            <div className="xl:col-span-3 space-y-6">
              {/* منطقة الاستعلام */}
              <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-6">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                  <Send className="w-6 h-6 mr-3 text-blue-600" />
                  إدخال الاستعلام
                </h2>
                <QueryInput
                  onSubmit={handleQuerySubmit}
                  isProcessing={isProcessing}
                  placeholder="اسأل أي سؤال عن بياناتك... مثل: أعرض لي أكثر 5 منتجات مبيعاً"
                />
              </div>

              {/* أمثلة الاستعلامات */}
              {queryHistory.length === 0 && (
                <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <Bot className="w-5 h-5 mr-2 text-blue-600" />
                    أمثلة على الاستعلامات
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {[
                      'أعرض لي أكثر 5 منتجات مبيعاً',
                      'قارن بين مبيعات هذا الشهر والشهر الماضي',
                      'ما هي أفضل الفروع أداءً؟',
                      'أكثر العملاء شراءً في الربع الأخير',
                      'تفاصيل المنتج الأكثر ربحية',
                      'مبيعات اليوم مقارنة بالأمس'
                    ].map((example, index) => (
                      <button
                        key={index}
                        onClick={() => handleQuerySubmit(example)}
                        disabled={isProcessing}
                        className="text-right p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600 rounded-lg hover:from-blue-100 hover:to-indigo-100 dark:hover:from-gray-600 dark:hover:to-gray-500 transition-all duration-200 text-sm text-gray-700 dark:text-gray-300 disabled:opacity-50 border border-blue-200/50 dark:border-gray-600 hover:shadow-md"
                      >
                        {example}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* عرض النتائج الحالية */}
              {queryHistory.length > 0 && (
                <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50">
                  <div className="p-6 border-b border-gray-200/50 dark:border-gray-700/50">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                      <BarChart3 className="w-6 h-6 mr-3 text-blue-600" />
                      النتيجة الأخيرة
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                      {queryHistory[0].query}
                    </p>
                  </div>
                  <div className="p-6">
                    <ResultsDisplay
                      queryHistory={[queryHistory[0]]}
                      onQuerySelect={(result) => {
                        setCurrentQuery(result.query);
                      }}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* الجانب الأيمن - سجل النتائج */}
            <div className="xl:col-span-1">
              <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 h-[calc(100vh-300px)] flex flex-col">
                <div className="p-4 border-b border-gray-200/50 dark:border-gray-700/50">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
                    سجل النتائج
                    {queryHistory.length > 0 && (
                      <span className="mr-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-300">
                        {queryHistory.length}
                      </span>
                    )}
                  </h3>
                </div>

                <div className="flex-1 overflow-y-auto p-4">
                  {queryHistory.length > 0 ? (
                    <div className="space-y-3">
                      {queryHistory.map((result, index) => (
                        <div
                          key={result.id}
                          className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                          onClick={() => setCurrentQuery(result.query)}
                        >
                          <div className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                            {result.query.length > 50 ? `${result.query.substring(0, 50)}...` : result.query}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center justify-between">
                            <span>{result.data.length} صف</span>
                            <span>{new Date(result.timestamp).toLocaleTimeString('ar-SA')}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        لا توجد نتائج بعد
                      </p>
                      <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                        ابدأ بإدخال استعلام
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'cache' && (
          <CacheManager />
        )}
      </main>

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />

      {/* Performance Monitor */}
      <PerformanceMonitor
        isVisible={showPerformance}
        onToggle={() => setShowPerformance(!showPerformance)}
      />
    </div>
  );
}
