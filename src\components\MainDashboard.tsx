'use client';

import { useState } from 'react';
import { Send, Database, BarChart3, MessageSquare, Settings, TrendingUp, Bot } from 'lucide-react';
import UnifiedChatInterface from './UnifiedChatInterface';
import ResultsDisplay from './ResultsDisplay';
import CacheManager from './CacheManager';
import SettingsPanel from './SettingsPanel';
import PerformanceMonitor from './PerformanceMonitor';
import { useQuickNotifications } from './NotificationSystem';

interface MainDashboardProps {
  connectionData: any;
}

interface QueryResult {
  id: string;
  query: string;
  sql: string;
  data: any[];
  analysis: string;
  timestamp: Date;
  visualization?: string;
  success?: boolean;
  cached?: boolean;
}

interface QueryContext {
  query: string;
  results: any[];
  sql: string;
  timestamp: number;
}

export default function MainDashboard({ connectionData }: MainDashboardProps) {
  const [currentQuery, setCurrentQuery] = useState('');
  const [queryHistory, setQueryHistory] = useState<QueryResult[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState<'chat' | 'results' | 'cache'>('chat');
  const [showSettings, setShowSettings] = useState(false);
  const [showPerformance, setShowPerformance] = useState(false);
  const [queryContext, setQueryContext] = useState<QueryContext | null>(null);
  const [showChatSidebar, setShowChatSidebar] = useState(false);
  const notifications = useQuickNotifications();

  const handleQuerySubmit = async (query: string) => {
    if (!query.trim() || isProcessing) return;

    setIsProcessing(true);
    setCurrentQuery(query);

    try {
      // إرسال الاستعلام للمعالجة
      const response = await fetch('/api/query/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          connectionData
        }),
      });

      const result = await response.json();

      if (response.ok) {
        const newResult: QueryResult = {
          id: Date.now().toString(),
          query,
          sql: result.sql,
          data: result.data,
          analysis: result.analysis,
          timestamp: new Date(),
          visualization: result.visualization
        };

        setQueryHistory(prev => [newResult, ...prev]);
        // البقاء في الواجهة الموحدة لعرض النتائج مباشرة

        // حفظ السياق للدردشة
        setQueryContext({
          query,
          results: result.data,
          sql: result.sql,
          timestamp: Date.now()
        });

        // تفعيل الدردشة تلقائياً بعد النتائج
        setShowChatSidebar(true);

        // إشعار نجاح
        notifications.success(
          'تم تنفيذ الاستعلام بنجاح',
          `تم العثور على ${result.data.length} صف من البيانات${result.cached ? ' (من التخزين المؤقت)' : ''}`
        );
      } else {
        notifications.error('خطأ في تنفيذ الاستعلام', result.error);
      }
    } catch (error) {
      console.error('خطأ في الشبكة:', error);
      notifications.error('فشل في معالجة الاستعلام', 'تحقق من الاتصال بالإنترنت وحاول مرة أخرى');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 shadow-lg border-b border-gray-200/50 dark:border-gray-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center mr-3">
                <Bot className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  وكيل الذكاء الاصطناعي - دردشة موحدة
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  متصل بـ: {connectionData.database}@{connectionData.server}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowChatSidebar(!showChatSidebar)}
                className={`p-2 rounded-lg transition-colors ${
                  showChatSidebar
                    ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                    : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                }`}
                title="الدردشة التفاعلية"
              >
                <MessageSquare className="w-5 h-5" />
              </button>
              <button
                onClick={() => setShowSettings(true)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title="الإعدادات"
              >
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 border-b border-gray-200/50 dark:border-gray-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('chat')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'chat'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <MessageSquare className="w-4 h-4 inline mr-2" />
              الدردشة الذكية
            </button>

            <button
              onClick={() => setActiveTab('results')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'results'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <BarChart3 className="w-4 h-4 inline mr-2" />
              سجل النتائج
              {queryHistory.length > 0 && (
                <span className="mr-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-300">
                  {queryHistory.length}
                </span>
              )}
            </button>

            <button
              onClick={() => setActiveTab('cache')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'cache'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Database className="w-4 h-4 inline mr-2" />
              التخزين المؤقت
            </button>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className={`${activeTab === 'chat' ? 'px-4 py-4' : 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'}`}>
        {activeTab === 'chat' && (
          <UnifiedChatInterface
            connectionData={connectionData}
            onQueryResult={(result) => {
              // إضافة النتيجة لسجل النتائج
              setQueryHistory(prev => [result, ...prev]);

              // تحديث السياق للدردشة
              setQueryContext({
                query: result.query,
                results: result.data,
                sql: result.sql,
                timestamp: Date.now()
              });

              // إشعار نجاح
              notifications.success(
                'تم تنفيذ الاستعلام بنجاح',
                `تم العثور على ${result.data.length} صف من البيانات`
              );
            }}
          />
        )}

        {activeTab === 'results' && (
          <div className="space-y-6">
            <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <BarChart3 className="w-6 h-6 mr-2 text-blue-600" />
                سجل جميع النتائج
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                عرض تفصيلي لجميع الاستعلامات والنتائج السابقة من الدردشة الذكية
              </p>
            </div>

            {queryHistory.length > 0 ? (
              <ResultsDisplay
                queryHistory={queryHistory}
                onQuerySelect={(result) => {
                  setCurrentQuery(result.query);
                  setActiveTab('chat');
                }}
              />
            ) : (
              <div className="bg-white/80 backdrop-blur-lg dark:bg-gray-800/80 rounded-xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-8 text-center">
                <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  لا توجد نتائج بعد
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  ابدأ بطرح سؤال في الدردشة الذكية لرؤية النتائج هنا
                </p>
                <button
                  onClick={() => setActiveTab('chat')}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
                >
                  انتقل إلى الدردشة الذكية
                </button>
              </div>
            )}
          </div>
        )}

        {activeTab === 'cache' && (
          <CacheManager />
        )}
      </main>

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />

      {/* Performance Monitor */}
      <PerformanceMonitor
        isVisible={showPerformance}
        onToggle={() => setShowPerformance(!showPerformance)}
      />
    </div>
  );
}
