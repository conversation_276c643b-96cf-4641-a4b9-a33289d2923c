{"queries": [{"id": "db1fa44eb12e1eb93d8151eacf3398fd", "query": "أعرض لي أكثر المنتجات مبيعاً", "sql": "SELECT TOP 5 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE DocumentName = 'فاتورة مبيعات'\n            GROUP BY ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330, "TotalRevenue": 14850}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330, "TotalRevenue": 594000}], "analysis": "تحليل البيانات يشير إلى أن هناك ثلاثة منتجات رئيسية تسيطر على المبيعات والإيرادات. 'قرص صلب1TB' باع 363 وحدة بإيرادات 101,640، 'ميكروويف' باع 346 وحدة بإيرادات 110,720، و'مكيف هواء' باع 330 وحدة بإيرادات 594,000. هذه المنتجات تحظى باهتمام كبير من العملاء وتسهم بشكل كبير في الإيرادات الإجمالية.", "timestamp": "2025-07-26T00:05:40.952Z", "expiresAt": "2025-07-26T01:05:40.952Z", "executionTime": 6490, "dataHash": "3c22683de9cac3872cac53719a40d414"}, {"id": "6a549430d787912b949d078ba34f73b3", "query": "أعرض لي أكثر 5 منتجات مبيعاً", "sql": "SELECT TOP 5 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE DocumentName = 'فاتورة مبيعات'\n            GROUP BY ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330, "TotalRevenue": 14850}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330, "TotalRevenue": 594000}], "analysis": "البيانات تشير إلى أن أكثر 5 منتجات مبيعاً هي: قرص صلب 1TB، ميكروويف، طاولة جانبية، تمر خلاص، ومكيف هواء. يتصدر قرص صلب 1TB الكمية المباعة بإجمالي 363 وحدة، بينما يحتل مكيف الهواء المرتبة الأولى في الإيرادات بـ 594,000. تظهر البيانات تفاوتاً كبيراً في أداء المنتجات، مما يستوجب تحليلاً أعمق للاتجاهات السوقية وطلب العملاء.", "timestamp": "2025-07-26T01:00:11.199Z", "expiresAt": "2025-07-26T02:00:11.199Z", "executionTime": 4962, "dataHash": "3c22683de9cac3872cac53719a40d414"}, {"id": "07e9c331e4db891e769cee02b7f49457", "query": "اكثر ثلاثة منتجات مبيعاً ", "sql": "SELECT TOP 3 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE DocumentName = 'فاتورة مبيعات'\n            GROUP BY ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}], "analysis": "تحليل البيانات يشير إلى أن هناك تنافساً قوياً بين المنتجات الثلاثة الأكثر مبيعاً. يبدو أن 'ميكروويف' يحقق أعلى إيرادات على الرغم من أنه ثاني أكثر المنتجات مبيعاً، مما يشير إلى تسعير أعلى أو هامش ربح أكبر. بينما 'قرص صلب1TB' و'طاولة جانبية' يتنافسان على عدد الوحدات المباعة، يبدو أن الأول يحظى بأعلى قيمة مبيعات.", "timestamp": "2025-07-26T01:00:26.294Z", "expiresAt": "2025-07-26T02:00:26.294Z", "executionTime": 4454, "dataHash": "badfd4cc3fd7c81c7c2d1bf1839c83c3"}], "version": "1.0.0", "lastCleanup": "2025-07-26T01:00:06.244Z"}