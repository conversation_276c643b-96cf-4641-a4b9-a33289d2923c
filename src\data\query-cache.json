{"queries": [{"id": "6a549430d787912b949d078ba34f73b3", "query": "أعرض لي أكثر 5 منتجات مبيعاً", "sql": "SELECT TOP 5 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE DocumentName = 'فاتورة مبيعات'\n            GROUP BY ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330, "TotalRevenue": 14850}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330, "TotalRevenue": 594000}], "analysis": "تحليل البيانات يشير إلى أن المنتجات الخمسة الأكثر مبيعاً هي: قرص صلب 1TB، ميكروويف، طاولة جانبية، تمر خلاص، ومكيف هواء. هذه المنتجات تحقق إيرادات عالية وتغطي معظم الكمية المباعة. ومع ذلك، هناك تفاوت كبير في الإيرادات بين المنتجات، حيث يعد مكيف الهواء الأعلى إيراداً.", "timestamp": "2025-07-25T22:58:54.810Z", "expiresAt": "2025-07-25T23:58:54.810Z", "executionTime": 5602, "dataHash": "3c22683de9cac3872cac53719a40d414"}], "version": "1.0.0", "lastCleanup": "2025-07-25T22:06:16.569Z"}