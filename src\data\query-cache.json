{"queries": [{"id": "6a549430d787912b949d078ba34f73b3", "query": "أعرض لي أكثر 5 منتجات مبيعاً", "sql": "SELECT TOP 5 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE DocumentName = 'فاتورة مبيعات'\n            GROUP BY ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330, "TotalRevenue": 14850}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330, "TotalRevenue": 594000}], "analysis": "تحليل البيانات يشير إلى أن المنتجات الخمسة الأكثر مبيعاً هي: قرص صلب 1TB، ميكروويف، طاولة جانبية، تمر خلاص، ومكيف هواء. هذه المنتجات تحقق إيرادات عالية وتغطي معظم الكمية المباعة. ومع ذلك، هناك تفاوت كبير في الإيرادات بين المنتجات، حيث يعد مكيف الهواء الأعلى إيراداً.", "timestamp": "2025-07-25T22:58:54.810Z", "expiresAt": "2025-07-25T23:58:54.810Z", "executionTime": 5602, "dataHash": "3c22683de9cac3872cac53719a40d414"}, {"id": "db1fa44eb12e1eb93d8151eacf3398fd", "query": "أعرض لي أكثر المنتجات مبيعاً", "sql": "SELECT TOP 5 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE DocumentName = 'فاتورة مبيعات'\n            GROUP BY ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330, "TotalRevenue": 14850}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330, "TotalRevenue": 594000}], "analysis": "تحليل البيانات يشير إلى أن هناك ثلاثة منتجات رئيسية تهيمن على المبيعات والإيرادات. 'قرص صلب1TB' باع 363 وحدة وحقق إيرادات بقيمة 101,640، بينما 'ميكروويف' باع 346 وحدة وحقق إيرادات بقيمة 110,720. 'مكي<PERSON> هواء' باع 330 وحدة وحقق إيرادات بقيمة 594,000. هذه النتائج تشير إلى أن المنتجات الإلكترونية والمنزلية تحظى باهتمام كبير من قبل العملاء.", "timestamp": "2025-07-25T23:09:15.535Z", "expiresAt": "2025-07-26T00:09:15.535Z", "executionTime": 5895, "dataHash": "3c22683de9cac3872cac53719a40d414"}, {"id": "07e9c331e4db891e769cee02b7f49457", "query": "اكثر ثلاثة منتجات مبيعاً ", "sql": "SELECT TOP 3 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE DocumentName = 'فاتورة مبيعات'\n            GROUP BY ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}], "analysis": "تحليل البيانات يشير إلى أن قرص صلب 1TB هو المنتج الأكثر مبيعاً بكمية 363 وحدة، يليه ميكروويف بكمية 346 وحدة، ثم طاولة جانبية بكمية 344 وحدة. من حيث الإيرادات، ميكروويف هو الأعلى ب 110,720، يليه قرص صلب 1TB ب 101,640، ثم طاولة جانبية ب 75,680.", "timestamp": "2025-07-25T23:30:13.652Z", "expiresAt": "2025-07-26T00:30:13.652Z", "executionTime": 6268, "dataHash": "badfd4cc3fd7c81c7c2d1bf1839c83c3"}, {"id": "5066704ad49425eebeab85fdf3d2389e", "query": "اكثر منتجين مبيعاً ", "sql": "SELECT TOP 1 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE DocumentName = 'فاتورة مبيعات'\n            GROUP BY ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}], "analysis": "تحليل البيانات يشير إلى أن قرص صلب 1TB هو المنتج الرائد في المبيعات، حيث حقق مبيعات عالية وإيرادات كبيرة. يشير هذا إلى قوة الطلب على هذا المنتج وضرورة التركيز عليه في استراتيجية المبيعات.", "timestamp": "2025-07-25T23:35:13.924Z", "expiresAt": "2025-07-26T00:35:13.924Z", "executionTime": 4978, "dataHash": "b28d9d0e6f0595cb6644b626b9523ce7"}, {"id": "9adbb9ea9b96f4f9bad510c3691d0f8c", "query": "اعرض لي تفاصيل مبيعات قرص صلب 1TB", "sql": "SELECT TOP 5 ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE DocumentName = 'فاتورة مبيعات'\n            GROUP BY ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330, "TotalRevenue": 14850}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330, "TotalRevenue": 594000}], "analysis": "تحليل البيانات يشير إلى أن قرص صلب 1TB حقق مبيعات عالية مع إجمالي 363 وحدة مباعة بقيمة 101,640. مقارنةً بالمنتجات الأخرى، يظهر أداءً جيدًا في فئته.", "timestamp": "2025-07-25T23:35:42.613Z", "expiresAt": "2025-07-26T00:35:42.613Z", "executionTime": 3500, "dataHash": "3c22683de9cac3872cac53719a40d414"}], "version": "1.0.0", "lastCleanup": "2025-07-25T22:06:16.569Z"}