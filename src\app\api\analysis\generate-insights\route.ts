import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

// إعداد OpenRouter API
const openai = new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY || '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',
  baseURL: 'https://openrouter.ai/api/v1',
});

const MODEL_NAME = 'qwen/qwen-2.5-72b-instruct';

interface QueryResult {
  id: string;
  query: string;
  sql: string;
  data: any[];
  analysis: string;
  timestamp: Date;
  visualization?: string;
}

interface Insight {
  type: 'trend' | 'pattern' | 'anomaly' | 'recommendation';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: string;
}

export async function POST(request: NextRequest) {
  try {
    const { queryHistory, connectionData } = await request.json();

    if (!queryHistory || queryHistory.length === 0) {
      return NextResponse.json({
        success: true,
        insights: []
      });
    }

    // تحليل سجل الاستعلامات لتوليد رؤى ذكية
    const insights = await generateInsights(queryHistory);

    return NextResponse.json({
      success: true,
      insights
    });

  } catch (error: any) {
    console.error('خطأ في توليد التحليلات:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في توليد التحليلات',
        details: error.message
      },
      { status: 500 }
    );
  }
}

async function generateInsights(queryHistory: QueryResult[]): Promise<Insight[]> {
  try {
    // تجميع البيانات من جميع الاستعلامات
    const allData = queryHistory.flatMap(result => result.data);
    const queries = queryHistory.map(result => result.query);
    const analyses = queryHistory.map(result => result.analysis);

    const prompt = `
أنت محلل بيانات خبير ومستشار أعمال. مهمتك تحليل سجل الاستعلامات والبيانات لتوليد رؤى ذكية وتوصيات عملية.

سجل الاستعلامات:
${JSON.stringify(queries, null, 2)}

التحليلات السابقة:
${JSON.stringify(analyses, null, 2)}

عينة من البيانات (أول 50 سجل):
${JSON.stringify(allData.slice(0, 50), null, 2)}

إجمالي عدد الاستعلامات: ${queryHistory.length}
إجمالي عدد السجلات: ${allData.length}

يرجى تحليل هذه البيانات وتوليد رؤى ذكية وإرجاع النتيجة كـ JSON صحيح فقط بدون أي نص إضافي:

[
  {
    "type": "trend|pattern|anomaly|recommendation",
    "title": "عنوان الرؤية",
    "description": "وصف مفصل للرؤية والتوصية",
    "impact": "high|medium|low",
    "category": "sales|customers|products|financial|temporal"
  }
]

مهم جداً: أرجع JSON فقط بدون أي تفسيرات أو عناوين أو markdown.

أنواع الرؤى المطلوبة:
1. الاتجاهات (trends): اتجاهات المبيعات، نمو العملاء، تطور المنتجات
2. الأنماط (patterns): أنماط الشراء، سلوك العملاء، موسمية المبيعات
3. الشذوذات (anomalies): قيم غير طبيعية، انخفاضات مفاجئة، ذروات غير متوقعة
4. التوصيات (recommendations): إجراءات مقترحة، فرص تحسين، استراتيجيات

يجب أن تكون الرؤى:
- عملية وقابلة للتنفيذ
- مبنية على البيانات الفعلية
- مفيدة لاتخاذ القرارات التجارية
- واضحة ومفهومة باللغة العربية
`;

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت محلل بيانات خبير ومستشار أعمال تقدم رؤى ذكية وتوصيات عملية باللغة العربية.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 3000
    });

    const result = response.choices[0]?.message?.content;
    if (!result) {
      throw new Error('لم يتم الحصول على رد من النموذج اللغوي');
    }

    try {
      // محاولة تحليل JSON مباشرة
      const insights = JSON.parse(result);
      return Array.isArray(insights) ? insights : [];
    } catch (parseError) {
      console.error('❌ فشل التحليل المباشر:', parseError.message);

      // محاولة استخراج JSON من markdown
      const extractedJson = extractJsonFromMarkdown(result);
      if (extractedJson) {
        console.log('✅ تم استخراج JSON من markdown');
        try {
          const insights = JSON.parse(extractedJson);
          return Array.isArray(insights) ? insights : [];
        } catch (secondParseError) {
          console.error('❌ فشل تحليل JSON المستخرج:', secondParseError.message);
        }
      }

      // محاولة استخراج الرؤى من النص
      console.log('🔄 محاولة استخراج الرؤى من النص...');
      return parseInsightsFromText(result);
    }

  } catch (error) {
    console.error('خطأ في توليد الرؤى:', error);
    return [];
  }
}

function extractJsonFromMarkdown(text: string): string | null {
  try {
    // البحث عن JSON داخل code blocks
    const jsonBlockMatch = text.match(/```(?:json)?\s*(\[[\s\S]*?\])\s*```/);
    if (jsonBlockMatch) {
      return jsonBlockMatch[1];
    }

    // البحث عن JSON مباشرة في النص
    const jsonMatch = text.match(/(\[[\s\S]*?\])/);
    if (jsonMatch) {
      return jsonMatch[1];
    }

    // البحث عن JSON object
    const objectMatch = text.match(/(\{[\s\S]*?\})/);
    if (objectMatch) {
      return objectMatch[1];
    }

    return null;
  } catch (error) {
    console.error('خطأ في استخراج JSON:', error);
    return null;
  }
}

function parseInsightsFromText(text: string): Insight[] {
  const insights: Insight[] = [];
  
  // محاولة استخراج الرؤى من النص باستخدام regex أو تحليل بسيط
  const lines = text.split('\n').filter(line => line.trim());
  
  let currentInsight: Partial<Insight> = {};
  
  for (const line of lines) {
    if (line.includes('اتجاه') || line.includes('trend')) {
      if (currentInsight.title) {
        insights.push(currentInsight as Insight);
      }
      currentInsight = {
        type: 'trend',
        title: line.trim(),
        impact: 'medium',
        category: 'sales'
      };
    } else if (line.includes('نمط') || line.includes('pattern')) {
      if (currentInsight.title) {
        insights.push(currentInsight as Insight);
      }
      currentInsight = {
        type: 'pattern',
        title: line.trim(),
        impact: 'medium',
        category: 'customers'
      };
    } else if (line.includes('توصية') || line.includes('recommendation')) {
      if (currentInsight.title) {
        insights.push(currentInsight as Insight);
      }
      currentInsight = {
        type: 'recommendation',
        title: line.trim(),
        impact: 'high',
        category: 'sales'
      };
    } else if (currentInsight.title && !currentInsight.description) {
      currentInsight.description = line.trim();
    }
  }
  
  if (currentInsight.title && currentInsight.description) {
    insights.push(currentInsight as Insight);
  }
  
  // إضافة رؤى افتراضية إذا لم يتم العثور على أي رؤى
  if (insights.length === 0) {
    insights.push(
      {
        type: 'trend',
        title: 'تحليل الاتجاهات العامة',
        description: 'تم تحليل البيانات المتاحة وتحديد الاتجاهات الرئيسية في الأداء.',
        impact: 'medium',
        category: 'sales'
      },
      {
        type: 'recommendation',
        title: 'تحسين جمع البيانات',
        description: 'يُنصح بتنفيذ المزيد من الاستعلامات للحصول على رؤى أكثر تفصيلاً.',
        impact: 'high',
        category: 'sales'
      }
    );
  }
  
  return insights;
}
